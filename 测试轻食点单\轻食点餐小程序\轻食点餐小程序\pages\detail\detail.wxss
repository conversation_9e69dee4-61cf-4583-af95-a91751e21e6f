/* detail.wxss */
page {
  height: 100vh;
  background-color: #f8f8f8;
}

.container {
  min-height: calc(100vh - 120rpx);
  padding-bottom: 140rpx; /* 为底部操作栏留出空间 */
}

/* 商品图片样式 */
.image-container {
  position: relative;
  width: 100%;
  height: 500rpx;
}

.product-image {
  width: 100%;
  height: 100%;
}

.back-btn {
  position: absolute;
  top: 60rpx;
  left: 30rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 商品信息样式 */
.product-info {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.product-price {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b35;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 营养信息样式 */
.nutrition-info {
  margin-top: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
}

.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}

.nutrition-item {
  display: flex;
  justify-content: space-between;
  padding: 15rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 10rpx;
}

.nutrition-name {
  font-size: 24rpx;
  color: #666;
}

.nutrition-value {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
}

/* 通用标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

/* 规格选择样式 */
.spec-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.spec-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.spec-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  border: 2rpx solid #eee;
  border-radius: 15rpx;
  min-width: 120rpx;
}

.spec-item.active {
  border-color: #ff6b35;
  background-color: #fff5f2;
}

.spec-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.spec-price {
  font-size: 22rpx;
  color: #ff6b35;
}

/* 数量选择样式 */
.quantity-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn.disabled {
  opacity: 0.5;
}

.quantity-text {
  font-size: 32rpx;
  color: #333;
}

.quantity-value {
  font-size: 32rpx;
  color: #333;
  margin: 0 30rpx;
  min-width: 60rpx;
  text-align: center;
}

/* 商品详情样式 */
.detail-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  min-height: 200rpx; /* 确保有足够的高度显示内容 */
}

.detail-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  word-wrap: break-word;
  white-space: pre-wrap; /* 保持换行格式 */
}

/* 底部操作栏样式 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  z-index: 100;
}

.total-price {
  flex: 1;
}

.price-label {
  font-size: 24rpx;
  color: #666;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  flex: 1;
  max-width: 400rpx;
}

.cart-btn, .buy-btn {
  flex: 1;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
  white-space: nowrap;
  min-width: 0;
}

.cart-btn {
  background-color: #fff;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
}

.buy-btn {
  background-color: #ff6b35;
  color: #fff;
}