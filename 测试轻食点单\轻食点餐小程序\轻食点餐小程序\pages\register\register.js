// pages/register/register.js
Page({
  data: {
    phone: '',
    password: '',
    confirmPassword: '',
    canRegister: false
  },

  // 手机号输入
  onPhoneInput(e) {
    const phone = e.detail.value;
    this.setData({
      phone: phone
    });
    this.checkCanRegister();
  },

  // 密码输入
  onPasswordInput(e) {
    const password = e.detail.value;
    this.setData({
      password: password
    });
    this.checkCanRegister();
  },

  // 确认密码输入
  onConfirmPasswordInput(e) {
    const confirmPassword = e.detail.value;
    this.setData({
      confirmPassword: confirmPassword
    });
    this.checkCanRegister();
  },

  // 检查是否可以注册
  checkCanRegister() {
    const phoneReg = /^1[3-9]\d{9}$/;
    const canRegister = phoneReg.test(this.data.phone) &&
                        this.data.password.length >= 6 &&
                        this.data.password === this.data.confirmPassword;

    this.setData({
      canRegister: canRegister
    });
  },

  // 注册
  onRegister() {
    if (!this.data.canRegister) return;

    wx.showLoading({
      title: '注册中...'
    });

    // 模拟注册验证
    setTimeout(() => {
      wx.hideLoading();

      // 简单的注册逻辑，实际应用中应该调用后端API
      const { phone, password } = this.data;
      wx.showModal({
        title: '注册成功',
        content: `您已成功注册，手机号：${phone}，密码：${password}`,
        showCancel: false,
        success: () => {
          // 注册成功后跳转到登录页面
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      });
    }, 1500);
  }
})