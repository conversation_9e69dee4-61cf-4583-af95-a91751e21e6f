/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding-bottom: 20rpx;
}

/* 轮播图样式 */
.banner-swiper {
  width: 100%;
  height: 300rpx;
  margin-bottom: 20rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 搜索框样式 */
.search-container {
  padding: 0 30rpx 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-box icon {
  margin-right: 20rpx;
}

.search-box input {
  flex: 1;
  font-size: 28rpx;
}

.search-placeholder {
  color: #999;
}

/* 九宫格导航样式 */
.nav-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  transition: all 0.3s ease;
}

.nav-item:active {
  transform: scale(0.95);
  background-color: #f5f5f5;
  border-radius: 15rpx;
}

.nav-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #333;
}

/* 标题样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.title-more {
  font-size: 24rpx;
  color: #999;
}

/* 商品列表样式 */
.product-list {
  padding: 0 20rpx;
}

.product-item {
  display: flex;
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.product-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 15rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.product-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}

.add-cart-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-cart-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}
