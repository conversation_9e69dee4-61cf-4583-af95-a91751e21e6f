/* pages/register/register.wxss */
page {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  height: 100vh;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0 60rpx;
}

/* 顶部装饰 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 15rpx;
}

.app-slogan {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 注册表单 */
.register-form {
  flex: 1;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  padding: 60rpx 40rpx;
  margin-top: 40rpx;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-item {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 50rpx;
  padding: 25rpx 30rpx;
  margin-bottom: 30rpx;
}

.input-item icon {
  margin-right: 20rpx;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

.login-btn {
  width: 100%;
  background-color: #ff6b35;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 0;
  font-size: 32rpx;
  font-weight: bold;
}

.login-btn[disabled] {
  background-color: #ccc;
}