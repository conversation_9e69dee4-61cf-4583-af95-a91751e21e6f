// pages/login/login.js
Page({
  data: {
    username: '',
    password: '',
    agreed: true,
    rememberPassword: false,
    canLogin: false
  },

  onLoad(options) {
    // 检查是否从其他页面跳转过来
    this.fromPage = options.from || 'profile'

    // 检查是否有记住的密码
    this.loadRememberedPassword()
  },

  // 加载记住的密码
  loadRememberedPassword() {
    const rememberedAccount = wx.getStorageSync('rememberedAccount')
    if (rememberedAccount) {
      this.setData({
        username: rememberedAccount.username,
        password: rememberedAccount.password,
        rememberPassword: true
      })
      this.checkCanLogin()
    }
  },

  // 用户名输入
  onUsernameInput(e) {
    const username = e.detail.value
    this.setData({
      username: username
    })
    this.checkCanLogin()
  },

  // 密码输入
  onPasswordInput(e) {
    const password = e.detail.value
    this.setData({
      password: password
    })
    this.checkCanLogin()
  },

  // 检查是否可以登录
  checkCanLogin() {
    const canLogin = this.data.username.length >= 3 &&
                     this.data.password.length >= 6 &&
                     this.data.agreed

    this.setData({
      canLogin: canLogin
    })
  },

  // 记住密码选择
  onRememberChange(e) {
    const remember = e.detail.value.includes('remember')
    this.setData({
      rememberPassword: remember
    })
  },

  // 登录
  onLogin() {
    if (!this.data.canLogin) return

    wx.showLoading({
      title: '登录中...'
    })

    // 模拟登录验证
    setTimeout(() => {
      wx.hideLoading()

      // 简单的账号密码验证（实际应用中应该调用后端API）
      const { username, password } = this.data

      // 模拟一些测试账号
      const testAccounts = [
        { username: 'admin', password: '123456', nickname: '管理员' },
        { username: 'user001', password: '123456', nickname: '用户001' },
        { username: 'test', password: '123456', nickname: '测试用户' }
      ]

      // 检查手机号格式
      const isPhone = /^1[3-9]\d{9}$/.test(username)

      const account = testAccounts.find(acc =>
        acc.username === username || (isPhone && acc.username === 'admin')
      )

      if (account && account.password === password) {
        // 登录成功
        const userInfo = {
          avatarUrl: '/images/default-avatar.png',
          nickName: account.nickname,
          username: username,
          phone: isPhone ? username : '',
          loginType: 'account'
        }

        // 保存记住的密码
        if (this.data.rememberPassword) {
          wx.setStorageSync('rememberedAccount', {
            username: username,
            password: password
          })
        } else {
          wx.removeStorageSync('rememberedAccount')
        }

        this.loginSuccess(userInfo)
      } else {
        // 登录失败
        wx.showModal({
          title: '登录失败',
          content: '用户名或密码错误\n\n测试账号：\nadmin/123456\nuser001/123456\ntest/123456',
          showCancel: false
        })
      }
    }, 1500)
  },

  // 忘记密码
  onForgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系客服重置密码\n客服电话：************\n或发送邮件至：<EMAIL>',
      showCancel: false
    })
  },

  // 注册
  onRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    })
  },

  // 登录成功
  loginSuccess(userInfo) {
    // 保存用户信息
    wx.setStorageSync('userInfo', userInfo)
    
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    })

    // 返回上一页或跳转到个人中心
    setTimeout(() => {
      if (this.fromPage === 'profile') {
        wx.switchTab({
          url: '/pages/profile/profile'
        })
      } else {
        wx.navigateBack()
      }
    }, 1500)
  },



  // 协议变更
  onAgreementChange(e) {
    const agreed = e.detail.value.includes('agree')
    this.setData({
      agreed: agreed
    })
    this.checkCanLogin()
  },

  // 显示协议
  onShowAgreement(e) {
    const type = e.currentTarget.dataset.type
    const title = type === 'user' ? '用户协议' : '隐私政策'
    const content = type === 'user' 
      ? '这里是用户协议的内容...\n\n1. 用户权利和义务\n2. 服务条款\n3. 免责声明'
      : '这里是隐私政策的内容...\n\n1. 信息收集\n2. 信息使用\n3. 信息保护'

    wx.showModal({
      title: title,
      content: content,
      showCancel: false
    })
  },

  // 快速体验
  onQuickExperience() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
