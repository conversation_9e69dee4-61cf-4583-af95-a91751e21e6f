<!--category.wxml-->
<view class="container">
  <!-- 分类导航 -->
  <scroll-view class="category-nav" scroll-x="true">
    <view class="nav-list">
      <view class="nav-item {{currentCategory === item.type ? 'active' : ''}}"
            wx:for="{{categories}}" wx:key="type"
            bindtap="onCategoryTap" data-type="{{item.type}}">
        <text class="nav-text">{{item.name}}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="onSortTap">
      <text class="filter-text">{{sortText}}</text>
      <icon type="{{sortOrder === 'asc' ? 'success' : 'cancel'}}" size="12"></icon>
    </view>
    <view class="filter-item" bindtap="onFilterTap">
      <text class="filter-text">筛选</text>
      <icon type="search" size="12"></icon>
    </view>
  </view>

  <!-- 商品列表 -->
  <scroll-view class="product-scroll" scroll-y="true">
    <view class="product-grid">
      <view class="product-card" wx:for="{{filteredProducts}}" wx:key="id"
            bindtap="onProductTap" data-id="{{item.id}}">
        <image src="{{item.image}}" class="product-image" mode="aspectFill"></image>
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <text class="product-desc">{{item.description}}</text>
          <view class="product-bottom">
            <text class="product-price">¥{{item.price}}</text>
            <view class="add-btn" bindtap="onAddToCart" data-id="{{item.id}}" catchtap="true">
              <text class="add-text">+</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{filteredProducts.length === 0}}">
      <image src="/images/empty.png" class="empty-image"></image>
      <text class="empty-text">暂无商品</text>
    </view>
  </scroll-view>
</view>