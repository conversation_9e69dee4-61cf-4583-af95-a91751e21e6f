/* category.wxss */
page {
  height: 100vh;
  background-color: #f8f8f8;
}

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 分类导航样式 */
.category-nav {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.nav-list {
  display: flex;
  padding: 0 20rpx;
  white-space: nowrap;
}

.nav-item {
  padding: 30rpx 40rpx;
  margin-right: 20rpx;
  border-radius: 50rpx;
  transition: all 0.3s;
}

.nav-item.active {
  background-color: #ff6b35;
}

.nav-text {
  font-size: 28rpx;
  color: #666;
}

.nav-item.active .nav-text {
  color: #fff;
  font-weight: bold;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-text {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

/* 商品列表样式 */
.product-scroll {
  flex: 1;
  padding: 20rpx;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.product-card {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  height: 240rpx;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-desc {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b35;
}

.add-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}