# 轻食点餐微信小程序

一个功能完整的轻食点餐微信小程序，提供健康美味的轻食订购服务。

## 功能特性

### 🏠 首页
- 精美轮播图展示热门活动
- 智能搜索框快速查找商品
- 九宫格导航快速分类浏览
- 推荐商品列表展示
- 一键加入购物车

### 📂 商品分类
- 多种分类筛选（沙拉、汉堡、饮品、甜品、套餐等）
- 价格排序功能
- 网格布局商品展示
- 实时筛选和搜索

### 📱 商品详情
- 高清商品图片展示
- 详细商品信息和营养成分
- 规格选择（大小、配料等）
- 数量调整
- 加入购物车/立即购买

### 🛒 购物车
- 商品列表管理
- 数量增减调整
- 单选/全选功能
- 实时价格计算
- 批量删除和结算

### 👤 个人中心
- 用户信息展示
- 订单统计概览
- 收货地址管理
- 优惠券中心
- 收藏和浏览历史
- 设置和反馈

### 🔐 登录系统
- 账号密码登录
- 记住密码功能
- 忘记密码找回
- 用户注册引导

### 📍 地址管理
- 收货地址列表管理
- 添加/编辑地址信息
- 地图选点定位
- 设置默认地址
- 地址选择集成到订单流程

## 技术特点

- **响应式设计**：适配不同屏幕尺寸
- **组件化开发**：模块化代码结构
- **状态管理**：全局购物车状态管理
- **本地存储**：用户数据持久化
- **动画效果**：流畅的交互体验
- **现代UI**：简洁美观的界面设计

## 项目结构

```
├── pages/                 # 页面文件
│   ├── index/             # 首页
│   ├── category/          # 分类页
│   ├── detail/            # 详情页
│   ├── cart/              # 购物车
│   ├── profile/           # 个人中心
│   ├── login/             # 登录页面
│   ├── address/           # 地址管理
│   └── address-edit/      # 地址编辑
├── images/                # 图片资源
├── utils/                 # 工具函数
├── app.js                 # 小程序入口
├── app.json              # 全局配置
└── app.wxss              # 全局样式
```

## 开发说明

### 环境要求
- 微信开发者工具
- 小程序基础库版本 2.10.4+

### 安装运行
1. 下载项目代码
2. 使用微信开发者工具打开项目
3. 配置小程序AppID
4. 点击编译运行

### 测试账号
登录页面提供以下测试账号：
- 账号：admin，密码：123456
- 账号：user001，密码：123456
- 账号：test，密码：123456

### 数据说明
- 当前使用模拟数据
- 商品图片使用Unsplash提供的示例图片
- 购物车数据保存在本地存储中

## 功能扩展

可以进一步扩展的功能：
- 用户登录注册
- 在线支付
- 订单管理
- 地址管理
- 优惠券系统
- 评价系统
- 推送通知
- 数据统计

