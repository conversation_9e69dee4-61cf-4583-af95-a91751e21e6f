// pages/detail/detail.js
Page({
  data: {
    product: {},
    quantity: 1,
    selectedSpec: null,
    totalPrice: 0,
    selectedAddress: null,
    // 模拟商品数据
    products: [
      {
        id: 1,
        name: '凯撒沙拉',
        description: '新鲜蔬菜配特制凯撒酱，营养丰富，口感清爽',
        price: 28.00,
        image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=400&fit=crop',
        detail: `【产品特色】
精选新鲜生菜、番茄、黄瓜等蔬菜，搭配特制凯撒�sauce汁，营养均衡，是健康饮食的首选。

【营养价值】
• 富含维生素A、C、K
• 高纤维，有助于消化
• 低热量，适合减脂人群
• 含有丰富的叶酸和抗氧化物质

【食用建议】
建议在制作后2小时内食用，口感最佳。可搭配全麦面包或坚果增加饱腹感。

【保存方式】
冷藏保存，建议当日食用完毕。`,
        nutrition: [
          { name: '热量', value: '120 kcal' },
          { name: '蛋白质', value: '8g' },
          { name: '脂肪', value: '6g' },
          { name: '碳水化合物', value: '12g' }
        ],
        specs: [
          { id: 1, name: '标准', extraPrice: 0 },
          { id: 2, name: '加量', extraPrice: 5 }
        ]
      },
      {
        id: 2,
        name: '牛油果吐司',
        description: '营养丰富的牛油果配全麦吐司',
        price: 32.00,
        image: 'https://images.unsplash.com/photo-1541519227354-08fa5d50c44d?w=400&h=400&fit=crop',
        detail: `【产品特色】
选用新鲜牛油果和优质全麦面包，富含健康脂肪和纤维，是早餐或轻食的完美选择。

【营养价值】
• 富含不饱和脂肪酸，有益心血管健康
• 高纤维，促进肠道健康
• 含有丰富的钾、维生素E和叶酸
• 提供优质蛋白质和复合碳水化合物

【制作工艺】
选用成熟度适中的牛油果，搭配烘烤至金黄的全麦吐司，现做现吃，保证最佳口感。

【适用人群】
适合健身人群、减脂人群以及注重健康饮食的消费者。`,
        nutrition: [
          { name: '热量', value: '280 kcal' },
          { name: '蛋白质', value: '12g' },
          { name: '脂肪', value: '18g' },
          { name: '碳水化合物', value: '25g' }
        ]
      },
      {
        id: 3,
        name: '鸡胸肉沙拉',
        description: '高蛋白低脂肪，健身首选',
        price: 35.00,
        image: 'https://images.unsplash.com/photo-1546793665-c74683f339c1?w=400&h=400&fit=crop',
        detail: `【产品特色】
精选优质鸡胸肉，低温慢煮保持嫩滑口感，搭配多种新鲜蔬菜，是健身人士的理想选择。

【营养价值】
• 高蛋白质，每份含25g优质蛋白
• 低脂肪，脂肪含量仅4g
• 富含B族维生素，促进新陈代谢
• 含有丰富的硒和磷，增强免疫力

【烹饪工艺】
采用低温慢煮技术，保持鸡胸肉的嫩滑口感，避免传统烹饪方式导致的肉质干柴问题。

【健身推荐】
特别适合增肌减脂人群，建议运动后30分钟内食用，有助于肌肉恢复和生长。`,
        nutrition: [
          { name: '热量', value: '180 kcal' },
          { name: '蛋白质', value: '25g' },
          { name: '脂肪', value: '4g' },
          { name: '碳水化合物', value: '8g' }
        ],
        specs: [
          { id: 1, name: '100g', extraPrice: 0 },
          { id: 2, name: '150g', extraPrice: 8 },
          { id: 3, name: '200g', extraPrice: 15 }
        ]
      },
      {
        id: 4,
        name: '经典汉堡',
        description: '经典牛肉汉堡，口感丰富',
        price: 42.00,
        image: 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=400&fit=crop',
        detail: `【产品特色】
选用鲜嫩多汁的牛肉饼，搭配新鲜蔬菜、芝士和特制酱料，口感丰富，是汉堡爱好者的经典之选。

【营养价值】
• 富含优质蛋白质，提供能量
• 含有多种维生素和矿物质
• 碳水化合物可补充体力

【制作工艺】
牛肉饼经过精心煎制，保持鲜嫩多汁。蔬菜新鲜爽脆，芝士融化后香气四溢。

【食用建议】
搭配薯条和可乐，享受一顿美味的快餐。`,
        nutrition: [
          { name: '热量', value: '450 kcal' },
          { name: '蛋白质', value: '20g' },
          { name: '脂肪', value: '25g' },
          { name: '碳水化合物', value: '35g' }
        ],
        specs: [
          { id: 1, name: '标准', extraPrice: 0 },
          { id: 2, name: '双份牛肉', extraPrice: 10 }
        ]
      },
      {
        id: 5,
        name: '鸡肉汉堡',
        description: '嫩滑鸡胸肉配新鲜蔬菜',
        price: 38.00,
        image: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=400&fit=crop',
        detail: `【产品特色】
选用嫩滑的鸡胸肉，搭配新鲜蔬菜和特制酱料，口感清爽不油腻。

【营养价值】
• 富含优质蛋白质，有助于肌肉修复和生长
• 含有多种维生素和矿物质，维持身体正常机能
• 相对低脂肪，适合关注健康的人群

【制作工艺】
鸡胸肉经过腌制和煎制，保留鲜嫩口感。蔬菜清洗干净，保证新鲜卫生。

【食用建议】
可搭配沙拉或饮品，营养更均衡。`,
        nutrition: [
          { name: '热量', value: '380 kcal' },
          { name: '蛋白质', value: '22g' },
          { name: '脂肪', value: '18g' },
          { name: '碳水化合物', value: '30g' }
        ],
        specs: [
          { id: 1, name: '标准', extraPrice: 0 },
          { id: 2, name: '加量', extraPrice: 8 }
        ]
      },
      {
        id: 6,
        name: '鲜榨橙汁',
        description: '100%纯天然橙汁',
        price: 18.00,
        image: 'https://images.unsplash.com/photo-1613478223719-2ab802602423?w=400&h=400&fit=crop',
        detail: `【产品特色】
采用新鲜橙子现榨而成，无添加，保留橙子的原汁原味和丰富营养。

【营养价值】
• 富含维生素 C，增强免疫力
• 含有多种有机酸和矿物质，促进消化
• 提供天然糖分，补充能量

【制作工艺】
挑选新鲜成熟的橙子，洗净后直接榨汁，不添加任何香精、色素和防腐剂。

【饮用建议】
建议在榨取后尽快饮用，口感最佳。可根据个人口味加入适量冰块。`,
        nutrition: [
          { name: '热量', value: '110 kcal' },
          { name: '维生素 C', value: '70mg' },
          { name: '碳水化合物', value: '25g' }
        ],
        specs: [
          { id: 1, name: '小杯（300ml）', extraPrice: 0 },
          { id: 2, name: '大杯（500ml）', extraPrice: 5 }
        ]
      },
      {
        id: 7,
        name: '柠檬蜂蜜茶',
        description: '清香柠檬配天然蜂蜜',
        price: 22.00,
        image: 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=400&h=400&fit=crop',
        detail: `【产品特色】
精选新鲜柠檬和天然蜂蜜，融合出清新香甜的口感，具有美容养颜、润肺止咳的功效。

【营养价值】
• 柠檬富含维生素 C 和柠檬酸，抗氧化、促进消化
• 蜂蜜含有多种维生素和矿物质，滋养身体、润肠通便

【制作工艺】
将新鲜柠檬切片，与天然蜂蜜一起冲泡，保留柠檬和蜂蜜的营养成分。

【饮用建议】
热饮适合冬天暖身，冷饮适合夏天解暑。可根据个人口味调整蜂蜜用量。`,
        nutrition: [
          { name: '热量', value: '130 kcal' },
          { name: '维生素 C', value: '30mg' },
          { name: '碳水化合物', value: '30g' }
        ],
        specs: [
          { id: 1, name: '标准杯（400ml）', extraPrice: 0 },
          { id: 2, name: '加大杯（600ml）', extraPrice: 6 }
        ]
      },
      {
        id: 8,
        name: '提拉米苏',
        description: '经典意式甜品',
        price: 45.00,
        image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=400&fit=crop',
        detail: `【产品特色】
正宗意式提拉米苏，采用马斯卡彭芝士、手指饼干和咖啡酒等原料，口感细腻，层次丰富。

【营养价值】
• 含有丰富的蛋白质和脂肪，提供能量
• 咖啡和可可粉含有咖啡因，提神醒脑

【制作工艺】
将马斯卡彭芝士打发，与蛋黄、糖等混合均匀。手指饼干浸泡咖啡酒，层层叠加，最后撒上可可粉。

【食用建议】
冷藏后食用口感更佳，可搭配咖啡或茶。`,
        nutrition: [
          { name: '热量', value: '520 kcal' },
          { name: '蛋白质', value: '8g' },
          { name: '脂肪', value: '30g' },
          { name: '碳水化合物', value: '50g' }
        ]
      }
    ]
  },

  onLoad(options) {
    const id = parseInt(options.id)
    const product = this.data.products.find(item => item.id === id)

    if (product) {
      this.setData({
        product: product,
        selectedSpec: product.specs ? product.specs[0].id : null
      })
      this.calculateTotalPrice()
      this.loadDefaultAddress()
    } else {
      wx.showToast({
        title: '商品不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载默认地址
  loadDefaultAddress() {
    const app = getApp()
    const defaultAddress = app.getDefaultAddress()

    this.setData({
      selectedAddress: defaultAddress
    })
  },

  // 返回上一页
  onBack() {
    wx.navigateBack()
  },

  // 规格选择
  onSpecTap(e) {
    const specId = e.currentTarget.dataset.id
    this.setData({
      selectedSpec: specId
    })
    this.calculateTotalPrice()
  },

  // 数量调整
  onQuantityChange(e) {
    const type = e.currentTarget.dataset.type
    let quantity = this.data.quantity

    if (type === 'plus') {
      quantity++
    } else if (type === 'minus' && quantity > 1) {
      quantity--
    }

    this.setData({
      quantity: quantity
    })
    this.calculateTotalPrice()
  },

  // 计算总价
  calculateTotalPrice() {
    let price = this.data.product.price

    // 加上规格额外费用
    if (this.data.selectedSpec && this.data.product.specs) {
      const spec = this.data.product.specs.find(item => item.id === this.data.selectedSpec)
      if (spec) {
        price += spec.extraPrice
      }
    }

    const totalPrice = (price * this.data.quantity).toFixed(2)
    this.setData({
      totalPrice: totalPrice
    })
  },

  // 添加到购物车
  onAddToCart() {
    const cartItem = this.buildCartItem()
    const app = getApp()
    app.addToCart(cartItem)

    wx.showToast({
      title: '已添加到购物车',
      icon: 'success',
      duration: 1500
    })
  },

  // 立即购买
  onBuyNow() {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再进行购买',
        confirmText: '去登录',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login?from=detail'
            })
          }
        }
      })
      return
    }

    // 检查是否选择了地址
    if (!this.data.selectedAddress) {
      wx.showModal({
        title: '提示',
        content: '请先选择收货地址',
        confirmText: '选择地址',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/address/address?select=true'
            })
          }
        }
      })
      return
    }

    const cartItem = this.buildCartItem()

    wx.showModal({
      title: '确认购买',
      content: `${cartItem.name} x${cartItem.quantity}\n总价：¥${cartItem.totalPrice}`,
      confirmText: '确认购买',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.processBuyNow(cartItem)
        }
      }
    })
  },

  // 处理立即购买
  processBuyNow(cartItem) {
    wx.showLoading({
      title: '支付中...'
    })

    setTimeout(() => {
      wx.hideLoading()

      // 创建订单
      const app = getApp()
      const orderData = {
        items: [cartItem],
        totalPrice: cartItem.totalPrice,
        totalQuantity: cartItem.quantity,
        address: this.data.selectedAddress.fullAddress,
        addressInfo: this.data.selectedAddress,
        remark: ''
      }

      const order = app.createOrder(orderData)

      // 模拟订单状态变化
      setTimeout(() => {
        app.updateOrderStatus(order.id, 'processing')
      }, 3000)

      setTimeout(() => {
        app.updateOrderStatus(order.id, 'completed')
      }, 10000)

      wx.showToast({
        title: '购买成功！',
        icon: 'success',
        duration: 2000
      })

      // 显示订单信息
      setTimeout(() => {
        wx.showModal({
          title: '订单创建成功',
          content: `订单号：${order.id}\n您可以在个人中心查看订单详情`,
          confirmText: '查看订单',
          cancelText: '继续购物',
          success: (modalRes) => {
            if (modalRes.confirm) {
              wx.switchTab({
                url: '/pages/profile/profile'
              })
            } else {
              wx.switchTab({
                url: '/pages/index/index'
              })
            }
          }
        })
      }, 2000)
    }, 1500)
  },

  // 构建购物车商品对象
  buildCartItem() {
    const product = this.data.product
    let spec = null

    if (this.data.selectedSpec && product.specs) {
      spec = product.specs.find(item => item.id === this.data.selectedSpec)
    }

    return {
      id: product.id,
      name: product.name,
      image: product.image,
      price: product.price,
      quantity: this.data.quantity,
      spec: spec,
      totalPrice: this.data.totalPrice
    }
  }
})