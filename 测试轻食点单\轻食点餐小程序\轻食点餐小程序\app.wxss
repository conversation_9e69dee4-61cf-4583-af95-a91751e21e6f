/**app.wxss**/
/* 全局样式重置 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 通用容器样式 */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 通用按钮样式 */
.btn {
  border-radius: 50rpx;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: #ff6b35;
  color: #fff;
}

.btn-secondary {
  background-color: #fff;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
}

.btn-disabled {
  background-color: #ccc;
  color: #999;
}

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 通用文本样式 */
.text-primary {
  color: #ff6b35;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

/* 通用间距样式 */
.m-10 { margin: 10rpx; }
.m-20 { margin: 20rpx; }
.m-30 { margin: 30rpx; }

.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }

.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 通用布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-around {
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 通用图片样式 */
.img-round {
  border-radius: 50%;
}

.img-rounded {
  border-radius: 15rpx;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 按钮点击效果 */
.btn:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 0 20rpx;
  }
}
